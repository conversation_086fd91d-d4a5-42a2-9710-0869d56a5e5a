.版本 2
.支持库 spec
.支持库 eAPI

.程序集 游戏程序集

.子程序 获取杆子地址, 长整数型
.局部变量 模块地址_基, 长整数型
.局部变量 temp, 长整数型
.局部变量 变量_手上的杆子, 长整数型

PID ＝ mem.进程_取进程ID (“rf4_x64.exe”)
' ' 手上的杆子
模块地址_基 ＝ mem.内存_取模块地址 (PID, “GameAssembly.dll”)

temp ＝ mem.内存_读长整数 (PID, 模块地址_基 ＋ 进制_十六到十 (“03D46140”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“B8”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“88”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“C0”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“90”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“20”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“28”), )
变量_手上的杆子 ＝ temp ＋ 进制_十六到十 (“80”)
返回 (变量_手上的杆子)

.子程序 处理收鱼
.局部变量 n, 整数型

n ＝ 0

.判断循环首 (判断是否需要按空格 () ＝ 0)
    n ＝ n ＋ 1
    延时 (10)
    .如果真 (n ＞ 100)
        跳出循环 ()
    .如果真结束

.判断循环尾 ()

n ＝ 0
.判断循环首 (判断是否需要按空格 () ＝ 1)
    n ＝ n ＋ 1
    延时 (10)
    .如果 (获取鱼护数量 () ≥ 100)
        mem.键盘_键盘按键 (#退格键, 1, )
    .否则
        mem.键盘_键盘按键 (#空格键, 1, )
    .如果结束


    .如果真 (n ＞ 101)
        跳出循环 ()
    .如果真结束

.判断循环尾 ()


.子程序 判断是否需要按空格, 整数型
.局部变量 模块地址_基, 长整数型
.局部变量 temp, 长整数型
.局部变量 变量_pointerscan_result, 整数型

模块地址_基 ＝ mem.内存_取模块地址 (PID, “GameAssembly.dll”)

temp ＝ mem.内存_读长整数 (PID, 模块地址_基 ＋ 进制_十六到十 (“03D46440”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“B8”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“100”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“140”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“20”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“20”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“28”), )
变量_pointerscan_result ＝ mem.内存_读整数型 (PID, temp ＋ 进制_十六到十 (“20”), )
返回 (变量_pointerscan_result)

.子程序 秒收鱼
.局部变量 钩子的坐标x, 小数型
.局部变量 钩子的坐标z, 小数型
.局部变量 钩子的坐标y, 小数型
.局部变量 temp, 长整数型
.局部变量 钩子坐标x, 小数型
.局部变量 钩子坐标z, 小数型
.局部变量 钩子坐标y, 小数型
.局部变量 玩家X坐标, 小数型
.局部变量 玩家Z坐标, 小数型
.局部变量 玩家Y坐标, 小数型
.局部变量 鱼的x地址, 长整数型
.局部变量 鱼的z地址, 长整数型
.局部变量 鱼的y地址, 长整数型
.局部变量 变量_钩子1x, 小数型
.局部变量 变量_钩子2x, 小数型
.局部变量 变量_钩子3x, 小数型
.局部变量 变量_钩子1z, 小数型
.局部变量 变量_钩子1y, 小数型
.局部变量 变量_钩子2z, 小数型
.局部变量 变量_钩子2y, 小数型
.局部变量 变量_钩子3z, 小数型
.局部变量 变量_钩子3y, 小数型
.局部变量 变量_手上的杆子地址, 长整数型
.局部变量 变量_手上的杆子, 整数型
.局部变量 钩子3x地址, 长整数型
.局部变量 钩子2x地址, 长整数型
.局部变量 钩子1x地址, 长整数型
.局部变量 模块地址_基, 长整数型
.局部变量 变量_pointerscan_result, 整数型
.局部变量 鱼2的x地址, 长整数型
.局部变量 鱼2的z地址, 长整数型
.局部变量 鱼2的y地址, 长整数型
.局部变量 鱼3的x地址, 长整数型
.局部变量 鱼3的z地址, 长整数型
.局部变量 鱼3的y地址, 长整数型
.局部变量 手里杆子是几号, 整数型

模块地址_基 ＝ mem.内存_取模块地址 (PID, “UnityPlayer.dll”)
temp ＝ mem.内存_读长整数 (PID, 模块地址_基 ＋ 进制_十六到十 (“01C499B8”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“940”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“10”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“38”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“1C8”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“18”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“10”), )
鱼的x地址 ＝ temp ＋ 进制_十六到十 (“A0”)
鱼的z地址 ＝ 鱼的x地址 ＋ 4
鱼的y地址 ＝ 鱼的x地址 ＋ 8





模块地址_基 ＝ mem.内存_取模块地址 (PID, “UnityPlayer.dll”)
temp ＝ mem.内存_读长整数 (PID, 模块地址_基 ＋ 进制_十六到十 (“01CA9B18”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“A0”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“80”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“30”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“8”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“B8”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“48”), )


玩家X坐标 ＝ mem.内存_读小数型 (PID, temp ＋ 进制_十六到十 (“90”), )
玩家Z坐标 ＝ mem.内存_读小数型 (PID, temp ＋ 进制_十六到十 (“94”), )
玩家Y坐标 ＝ mem.内存_读小数型 (PID, temp ＋ 进制_十六到十 (“98”), )




.如果真 (杆子1状态 ＝ 1 或 杆子2状态 ＝ 1 或 杆子3状态 ＝ 1)

    ' ' pointerscan result
    模块地址_基 ＝ mem.内存_取模块地址 (PID, “GameAssembly.dll”)
    temp ＝ mem.内存_读长整数 (PID, 模块地址_基 ＋ 进制_十六到十 (“03D55718”), )
    temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“B8”), )
    temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“0”), )
    temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“10”), )
    temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“38”), )
    temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“C0”), )
    变量_pointerscan_result ＝ mem.内存_读整数型 (PID, temp ＋ 进制_十六到十 (“90”), )  ' 上鱼

    .如果真 (变量_pointerscan_result ＝ 1)

        mem.内存_写小数型 (PID, 鱼的x地址, 玩家X坐标, 0)
        mem.内存_写小数型 (PID, 鱼的z地址, 玩家Z坐标, 0)
        mem.内存_写小数型 (PID, 鱼的y地址, 玩家Y坐标, 0)
        调试输出 (取现行时间 (), “收鱼完成”, 玩家X坐标, 进制_十到十六 (鱼的x地址), 鱼的z地址, 鱼的y地址)
    .如果真结束

.如果真结束


.子程序 判断鱼的数量, 文本型
.局部变量 水平比例, 小数型
.局部变量 垂直比例, 小数型
.局部变量 颜色值, 文本型
.局部变量 游戏窗口宽度, 双精度小数型
.局部变量 游戏窗口长度, 双精度小数型
.局部变量 坐标x, 双精度小数型
.局部变量 坐标y, 双精度小数型
.局部变量 Rect1, 精易_矩形
.局部变量 b, 长整数型
.局部变量 长度, 双精度小数型
.局部变量 宽度, 双精度小数型
.局部变量 宽度比例, 双精度小数型
.局部变量 长度比例, 双精度小数型
.局部变量 文字, 文本型
.局部变量 图片数据, 字节集
.局部变量 data, 整数型
.局部变量 size, 整数型
.局部变量 鱼的数量Rect, 矩形数据

Rect1 ＝ 窗口_取矩形 (窗口句柄)
游戏窗口宽度 ＝ Rect1.右边 － Rect1.左边
游戏窗口长度 ＝ Rect1.底边 － Rect1.顶边
水平比例 ＝ 1204 ÷ 1280
水平比例 ＝ 1522 ÷ 1600
垂直比例 ＝ 500 ÷ 768
垂直比例 ＝ 628 ÷ 900
宽度比例 ＝ 16 ÷ 1280
长度比例 ＝ 14 ÷ 768
坐标x ＝ Rect1.左边 ＋ 游戏窗口宽度 × 水平比例
坐标y ＝ Rect1.顶边 ＋ 游戏窗口长度 × 垂直比例
宽度 ＝ 游戏窗口宽度 × 宽度比例
长度 ＝ 游戏窗口长度 × 长度比例
窗口_将焦点切换到指定的窗口 (窗口句柄, 真)
' 保存截图
鱼的数量Rect.顶边 ＝ 坐标y
鱼的数量Rect.底边 ＝ 坐标y ＋ 长度
鱼的数量Rect.左边 ＝ 坐标x
鱼的数量Rect.右边 ＝ 坐标x ＋ 宽度
图片数据 ＝ 截取屏幕区域 (鱼的数量Rect, 2, )
文字 ＝ identify (图片数据, 取字节集长度 (图片数据))
写到文件 (“C:\Users\<USER>\Desktop\1.jpg”, 图片数据)
返回 (文字)


.子程序 获取鱼护数量, 整数型
.局部变量 鱼的数量, 文本型

窗口_将焦点切换到指定的窗口 (窗口句柄, 真)
延时 (10)
鱼的数量 ＝ 判断鱼的数量 ()
调试输出 (“目前数量：” ＋ 鱼的数量)
返回 (到整数 (鱼的数量))

.子程序 判断点的位置颜色是否正确, 逻辑型
.参数 x, 整数型
.参数 y, 整数型
.参数 颜色值, 文本型
.参数 起点位置, 整数型, , 0为左上角，1为左下角
.局部变量 游戏窗口宽度, 双精度小数型
.局部变量 游戏窗口长度, 双精度小数型
.局部变量 坐标x, 双精度小数型
.局部变量 坐标y, 双精度小数型
.局部变量 Rect1, 精易_矩形
.局部变量 b, 长整数型
.局部变量 颜色, 整数型

Rect1 ＝ 窗口_取矩形 (窗口句柄)
游戏窗口宽度 ＝ Rect1.右边 － Rect1.左边
游戏窗口长度 ＝ Rect1.底边 － Rect1.顶边
.如果真 (起点位置 ＝ 0)
    坐标x ＝ Rect1.左边 ＋ x
    坐标y ＝ Rect1.顶边 ＋ y

.如果真结束

.如果真 (起点位置 ＝ 1)
    坐标x ＝ Rect1.左边 ＋ x
    坐标y ＝ Rect1.底边 － y
.如果真结束

' 大漠.鼠标_移动 (坐标x, 坐标y)

' 调试输出 (Rect1.左边, Rect1.顶边, 大漠.图色_颜色比较 (坐标x, 坐标y, 颜色值, 0.8))

b ＝ 大漠.图色_颜色比较 (坐标x, 坐标y, 颜色值, 0.8)


.如果真 (b ＝ 0)
    返回 (真)
.如果真结束
返回 (假)

.子程序 传送
.参数 x坐标, 小数型
.参数 z坐标, 小数型
.参数 y坐标, 小数型
.局部变量 模块地址_基, 长整数型
.局部变量 temp, 长整数型
.局部变量 变量_人x, 小数型
.局部变量 变量_人z, 小数型
.局部变量 变量_人y, 小数型

' ' 人x
模块地址_基 ＝ mem.内存_取模块地址 (PID, “UnityPlayer.dll”)
temp ＝ mem.内存_读长整数 (PID, 模块地址_基 ＋ 进制_十六到十 (“01CA9B18”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“A0”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“80”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“30”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“8”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“B8”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“48”), )



mem.内存_写小数型 (PID, temp ＋ 进制_十六到十 (“90”), x坐标, )
mem.内存_写小数型 (PID, temp ＋ 进制_十六到十 (“94”), z坐标, )
mem.内存_写小数型 (PID, temp ＋ 进制_十六到十 (“98”), y坐标, )

.子程序 读当前坐标, D3D坐标
.局部变量 模块地址_基, 长整数型
.局部变量 temp, 长整数型
.局部变量 人物坐标, D3D坐标

模块地址_基 ＝ mem.内存_取模块地址 (PID, “UnityPlayer.dll”)
temp ＝ mem.内存_读长整数 (PID, 模块地址_基 ＋ 进制_十六到十 (“01CA9B18”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“A0”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“80”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“30”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“8”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“B8”), )
temp ＝ mem.内存_读长整数 (PID, temp ＋ 进制_十六到十 (“48”), )



人物坐标.x ＝ mem.内存_读小数型 (PID, temp ＋ 进制_十六到十 (“90”), )
人物坐标.z ＝ mem.内存_读小数型 (PID, temp ＋ 进制_十六到十 (“94”), )
人物坐标.y ＝ mem.内存_读小数型 (PID, temp ＋ 进制_十六到十 (“98”), )
返回 (人物坐标)
